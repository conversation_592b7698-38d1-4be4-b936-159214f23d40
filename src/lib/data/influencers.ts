import { Influencer } from '../../types'

export const influencersData: Influencer[] = [
  {
    id: 'alix-earle',
    name: '<PERSON><PERSON>',
    username: 'alixear<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=400&h=400&fit=crop&crop=face',
    coverImage: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200&h=600&fit=crop',
    bio: "Miami-based lifestyle influencer known for trendy fashion and beauty content. I'm all about that effortless chic vibe with a touch of glam!",
    specialty: 'Lifestyle & Fashion',
    followers: '5.2M',
    categories: ['Fashion', 'Beauty', 'Lifestyle', 'Travel'],
    styleKeywords: ['trendy', 'chic', 'Miami vibes', 'effortless', 'glam', 'beach-ready'],
    socialLinks: {
      instagram: 'https://instagram.com/alixearle',
      tiktok: 'https://tiktok.com/@alixearle',
      youtube: 'https://youtube.com/@alixearle',
    },
    verified: true,
    rating: 4.8,
    totalRecommendations: 1247,
    isActive: true,
  },
  {
    id: 'emma-chamberlain',
    name: 'Emma Chamberlain',
    username: 'emmachamberlain',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
    coverImage: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=1200&h=600&fit=crop',
    bio: "Coffee-loving content creator with a unique casual-chic aesthetic. I believe style should be comfortable, sustainable, and uniquely you!",
    specialty: 'Casual Chic',
    followers: '12.1M',
    categories: ['Casual Wear', 'Vintage', 'Sustainable Fashion', 'Coffee Culture'],
    styleKeywords: ['casual', 'vintage', 'cozy', 'unique', 'laid-back', 'sustainable'],
    socialLinks: {
      instagram: 'https://instagram.com/emmachamberlain',
      youtube: 'https://youtube.com/@emmachamberlain',
      twitter: 'https://twitter.com/emmachamberlain',
    },
    verified: true,
    rating: 4.9,
    totalRecommendations: 2156,
    isActive: true,
  },
  {
    id: 'style-maven',
    name: 'Style Maven',
    username: 'stylemaven',
    avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=400&h=400&fit=crop&crop=face',
    coverImage: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200&h=600&fit=crop',
    bio: "High-end fashion curator with access to exclusive designer pieces. Helping you invest in timeless luxury that elevates your wardrobe.",
    specialty: 'Luxury Fashion',
    followers: '3.8M',
    categories: ['Luxury', 'Designer', 'High Fashion', 'Investment Pieces'],
    styleKeywords: ['luxury', 'timeless', 'sophisticated', 'designer', 'elegant', 'investment'],
    socialLinks: {
      instagram: 'https://instagram.com/stylemaven',
      twitter: 'https://twitter.com/stylemaven',
    },
    verified: true,
    rating: 4.7,
    totalRecommendations: 892,
    isActive: true,
  },
]

export async function getInfluencers(): Promise<Influencer[]> {
  await new Promise(resolve => setTimeout(resolve, 100))
  return influencersData
}

export async function getInfluencer(id: string): Promise<Influencer | null> {
  await new Promise(resolve => setTimeout(resolve, 100))
  return influencersData.find(influencer => influencer.id === id) || null
}