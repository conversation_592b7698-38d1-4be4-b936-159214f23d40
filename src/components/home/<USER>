"use client"

import { motion } from "framer-motion"
import { InfluencerCard } from "@/components/influencer/influencer-card"
import { influencersData } from "@/lib/data/influencers"
import { Button } from "../ui/button"
import { Link } from "lucide-react"

export function FeaturedInfluencers() {
  return (
    <div className="space-y-12">
      <div className="text-center space-y-4">
        <motion.h2 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-3xl lg:text-4xl font-bold"
        >
          Shop with Top Influencers
        </motion.h2>
        <motion.p 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="text-lg text-muted-foreground max-w-2xl mx-auto"
        >
          Get personalized style recommendations from verified influencers who know what's trending
        </motion.p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {influencersData.slice(0, 3).map((influencer, index) => (
          <motion.div
            key={influencer.id}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
          >
            <InfluencerCard influencer={influencer} />
          </motion.div>
        ))}
      </div>

      <div className="text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Button variant="outline" size="lg" asChild>
            <Link href="/influencers">
              View All Influencers
            </Link>
          </Button>
        </motion.div>
      </div>
    </div>
  )
}