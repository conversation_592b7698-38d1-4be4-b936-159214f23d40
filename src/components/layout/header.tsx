"use client"

import Link from "next/link"
import { <PERSON><PERSON><PERSON>, <PERSON>u, <PERSON> } from "lucide-react"
import { But<PERSON> } from "../ui/button"
import { useState } from "react"

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Sparkles className="h-8 w-8 text-primary" />
            <span className="text-2xl font-bold text-foreground">ATE Shop</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {/* Navigation links removed */}
          </nav>

          {/* Actions */}
          <div className="flex items-center space-x-4">
            <Button size="sm" className="hidden md:inline-flex">
              Contact
            </Button>
            
            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t">
            <nav className="flex flex-col space-y-3">
              <div className="pt-3 space-y-2">
                <Button size="sm" className="w-full">
                  Contact
                </Button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}