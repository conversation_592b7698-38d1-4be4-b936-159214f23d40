// src/app/api/chat/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { chatService } from '../../../lib/services/chat-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, influencerId, conversationId } = body

    if (!message || !influencerId) {
      return NextResponse.json(
        { error: 'Message and influencerId are required' },
        { status: 400 }
      )
    }

    const response = await chatService.processMessage({
      message,
      influencerId,
      conversationId,
    })

    return NextResponse.json(response)
  } catch (error) {
    console.error('Chat API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}