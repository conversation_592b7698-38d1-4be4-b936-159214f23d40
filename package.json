{"name": "ate-shop-v1", "version": "1.0.0", "private": true, "description": "AI-powered influencer shopping platform", "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@vercel/analytics": "^1.4.0", "babel-plugin-react-compiler": "^19.1.0-rc.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.11.17", "geist": "^1.3.1", "lucide-react": "^0.468.0", "nanoid": "^5.0.9", "next": "^15.1.0", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.5.4", "zod": "^3.23.8", "zustand": "^5.0.2"}, "devDependencies": {"@types/node": "^22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "autoprefixer": "^10.4.20", "eslint": "^9.16.0", "eslint-config-next": "^15.1.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}